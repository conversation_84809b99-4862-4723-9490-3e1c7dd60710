# Rigaddon Play - Professional Motion Toolkit

**Version:** 1.0.0  
**Author:** <PERSON><PERSON><PERSON>  
**Category:** Rigaddon  
**Blender Version:** 4.3.0+

## Overview

Rigaddon Play is a professional motion toolkit for Blender 4.3 that focuses on animating objects without bones/armature. Perfect for creating realistic motion effects like bouncing balls with jelly effects, trees swaying in wind, earthquake vibrations, machine vibrations, and much more.

## Features

### 🎯 **Core Features**
- **125+ Built-in Presets** across 9 categories
- **Modular Design** with clean, organized code
- **User-Friendly Interface** in N-panel → Rigaddon tab
- **Smart Preset System** with JSON-based storage
- **Multi-Object Support** with random delay options
- **Real-time Preview** and playback controls

### 📁 **Preset Categories**
1. **Vibration** (15 presets) - Shakes, earthquakes, impacts
2. **Magic** (15 presets) - Teleports, glitches, magical effects
3. **Swing** (20 presets) - Pendulum motions, oscillations
4. **Realistic** (25 presets) - Natural movements, physics-based
5. **Cinematic** (10 presets) - Camera movements, dramatic effects
6. **Mechanical** (10 presets) - Machine operations, rotations
7. **Environment** (10 presets) - Natural phenomena
8. **Experimental** (10 presets) - Creative, abstract motions
9. **Dynamic** (10 presets) - Breathing, pulsing, expanding

### 🎛️ **Interface Tabs**

#### **Presets Tab**
- **Filter & Sort**: Category filter, favorites, A-Z sorting
- **UI Modes**: UIList view or Button grid
- **Active Presets**: Track applied animations
- **Apply Settings**: Random delay, multi-object support
- **Preview Mode**: Temporary application for testing

#### **Animation Control Tab**
- **Animation Strips**: Manage active animations
- **Speed Control**: 0.25x to 10x speed multipliers
- **Timing Control**: Offset, stretch, shrink durations
- **Loop & Reverse**: Custom loop counts and direction
- **Blend & Influence**: Blend modes and influence control

#### **Preset Creator Tab**
- **Custom Presets**: Create and manage your own presets
- **Capture Keyframes**: Save existing animations as presets
- **Realtime Recording**: Record live object manipulation
- **Export/Import**: Share presets with others

#### **Help/Info Tab**
- **Quick Tutorial**: Step-by-step usage guide
- **Preset Statistics**: Count by category and favorites
- **Reset Options**: Restore default presets

### 🎮 **Playback Controls**
Always visible at the bottom of every tab:
- **Play Once**: Single playthrough
- **Loop**: Continuous looping
- **Play From Here**: Start from current frame
- **Play In Range**: Custom frame range
- **Stop/Pause**: Full control over playback

## Installation

1. Download the addon folder
2. Place in Blender's addons directory or install via Preferences
3. Enable "Rigaddon Play" in Add-ons preferences
4. Access via N-panel → Rigaddon tab

## Quick Start

1. **Select Object(s)**: Choose objects you want to animate
2. **Choose Preset**: Go to Presets tab, select from library
3. **Apply Motion**: Click Apply or use Button mode for instant application
4. **Fine-tune**: Use Animation Control tab for adjustments
5. **Play**: Use playback controls to preview results

## Workflow Examples

### Basic Animation
```
1. Select a cube
2. Presets Tab → Button Mode → Click "Ball Drop Jelly Strong"
3. Animation applied instantly
4. Use playback controls to preview
```

### Multi-Object with Delay
```
1. Select multiple objects
2. Enable "Random Delay" with max 20 frames
3. Apply any preset
4. Objects animate with staggered timing
```

### Custom Preset Creation
```
1. Animate an object manually
2. Preset Creator Tab → "Capture Keyframes"
3. Set name, category, duration
4. Save to custom library
```

### Speed and Timing Control
```
1. Apply preset to object
2. Animation Control Tab → Speed Control
3. Change to 0.5x for slow motion
4. Add 30-frame offset for delayed start
```

## File Structure

```
Rigaddon Play/
├── __init__.py              # Main addon file
├── preset_manager_rgp.py    # Preset loading/saving system
├── properties_rgp.py        # Blender properties
├── ui_panels_rgp.py         # User interface panels
├── operators_rgp.py         # Blender operators
├── playback_control_rgp.py  # Playback functionality
├── default_presets.json     # 125 built-in presets
├── custom_presets.json      # User-created presets
└── README.md               # This documentation
```

## Technical Details

### Naming Convention
All functions, variables, and operators use the `_rgp` suffix to prevent conflicts with other addons.

### Data Storage
- **Default Presets**: Stored in `default_presets.json` (read-only)
- **Custom Presets**: Stored in `custom_presets.json` (user-editable)
- **Favorites**: Stored in memory, reset on addon reload

### Keyframe Format
```json
{
  "name": "Preset Name",
  "category": "Category",
  "duration": 30,
  "loopable": true,
  "favorite": false,
  "keyframes": {
    "location": [
      {"frame": 1, "value": [0, 0, 0]},
      {"frame": 15, "value": [0, 0, 1]},
      {"frame": 30, "value": [0, 0, 0]}
    ],
    "rotation": [...],
    "scale": [...]
  }
}
```

## Troubleshooting

### Common Issues
- **No presets showing**: Click "Reset Default Presets" in Help tab
- **Addon not loading**: Check Blender 4.3+ compatibility
- **Animation not applying**: Ensure objects are selected
- **Custom presets not saving**: Check file permissions

### Reset Options
- **Reset Default Presets**: Restores original 125 presets
- **Clear Custom Presets**: Remove all user-created presets
- **Refresh UI**: Reload preset list display

## Support

For issues, suggestions, or contributions:
- Check the Help tab for built-in tutorials
- Use "Show Tutorial" for step-by-step guidance
- Reset default presets if library appears corrupted

## License

Created by Rigel Tapangan for the Rigaddon addon collection.

---

**Rigaddon Play** - Professional motion toolkit for Blender 4.3  
*Making object animation simple, powerful, and creative.*
