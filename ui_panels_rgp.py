"""
Rigaddon Play - UI Panels Module
Defines all UI panels for the N-panel interface
"""

import bpy
from bpy.types import Panel, UIList

class RIGADDON_PT_main_panel_rgp(Panel):
    """Main Rigaddon Play panel in N-panel"""
    bl_label = "Rigaddon Play"
    bl_idname = "RIGADDON_PT_main_panel_rgp"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "Rigaddon"
    
    def draw(self, context):
        layout = self.layout
        props = context.scene.rigaddon_play_props
        
        # Tab selector
        row = layout.row()
        row.prop(props, "active_tab", expand=True)
        
        # Draw content based on active tab
        if props.active_tab == "PRESETS":
            self.draw_presets_tab(context, layout)
        elif props.active_tab == "ANIMATION":
            self.draw_animation_tab(context, layout)
        elif props.active_tab == "CREATOR":
            self.draw_creator_tab(context, layout)
        elif props.active_tab == "HELP":
            self.draw_help_tab(context, layout)

        # Always show playback control at bottom
        layout.separator()
        self.draw_playback_control(context, layout)
    
    def draw_presets_tab(self, context, layout):
        """Draw Presets tab content"""
        props = context.scene.rigaddon_play_props
        
        # Filter and Sort section
        box = layout.box()
        box.label(text="Filter & Sort", icon='FILTER')
        
        col = box.column()
        col.prop(props, "category_filter", text="Category")
        col.prop(props, "favorites_only")
        col.prop(props, "sort_type", text="Sort")
        
        # UI Mode selector
        row = box.row()
        row.prop(props, "ui_mode", expand=True)
        
        # Preset display based on UI mode
        if props.ui_mode == "UILIST":
            self.draw_preset_uilist(context, layout)
        else:
            self.draw_preset_buttons(context, layout)
        
        # Active presets section
        box = layout.box()
        box.label(text="Active Presets", icon='PLAY')
        
        if len(props.active_preset_list) > 0:
            row = box.row()
            row.template_list(
                "RIGADDON_UL_active_presets_rgp", "",
                props, "active_preset_list",
                props, "active_preset_index"
            )
            
            col = row.column(align=True)
            col.operator("rigaddon.remove_active_preset_rgp", icon='X', text="")
            col.operator("rigaddon.mute_preset_rgp", icon='HIDE_OFF', text="")
            col.operator("rigaddon.solo_preset_rgp", icon='SOLO_ON', text="")
        else:
            box.label(text="No active presets", icon='INFO')
        
        # Apply settings
        box = layout.box()
        box.label(text="Apply Settings", icon='SETTINGS')
        
        col = box.column()
        col.prop(props, "random_delay_enabled")
        if props.random_delay_enabled:
            col.prop(props, "random_delay_max")
        
        col.prop(props, "preview_mode")
        
        # Preset info
        self.draw_preset_info(context, layout)

        # Initialize button if no presets loaded
        if len(props.preset_list) == 0:
            box = layout.box()
            box.label(text="No presets loaded", icon='INFO')
            box.operator("rigaddon.initialize_presets_rgp", text="Load Presets", icon='FILE_REFRESH')
    
    def draw_preset_uilist(self, context, layout):
        """Draw preset UIList"""
        props = context.scene.rigaddon_play_props
        
        box = layout.box()
        box.label(text="Preset Library", icon='PRESET')
        
        row = box.row()
        row.template_list(
            "RIGADDON_UL_presets_rgp", "",
            props, "preset_list",
            props, "preset_list_index"
        )
        
        col = row.column(align=True)
        col.operator("rigaddon.apply_preset_rgp", icon='PLAY', text="")
        col.operator("rigaddon.toggle_favorite_rgp", icon='SOLO_ON', text="")
        col.operator("rigaddon.delete_custom_preset_rgp", icon='X', text="")
        
        # Apply button
        row = box.row()
        row.operator("rigaddon.apply_preset_rgp", text="Apply Preset", icon='PLAY')
    
    def draw_preset_buttons(self, context, layout):
        """Draw preset buttons grid"""
        props = context.scene.rigaddon_play_props
        
        box = layout.box()
        box.label(text="Preset Library", icon='PRESET')
        
        # Grid of preset buttons (simplified for now)
        col = box.column()
        for i, preset in enumerate(props.preset_list):
            if i % 3 == 0:
                row = col.row()
            
            op = row.operator("rigaddon.apply_preset_by_name_rgp", text=preset.name)
            op.preset_name = preset.name
    
    def draw_animation_tab(self, context, layout):
        """Draw Animation Control tab content"""
        props = context.scene.rigaddon_play_props
        
        # Animation strips
        box = layout.box()
        box.label(text="Animation Strips", icon='NLA')
        
        if len(props.animation_strips) > 0:
            row = box.row()
            row.template_list(
                "RIGADDON_UL_animation_strips_rgp", "",
                props, "animation_strips",
                props, "animation_strip_index"
            )
            
            col = row.column(align=True)
            col.operator("rigaddon.remove_animation_strip_rgp", icon='X', text="")
        else:
            box.label(text="No active animations", icon='INFO')
        
        # Speed control
        box = layout.box()
        box.label(text="Speed Control", icon='TIME')
        
        col = box.column()
        col.prop(props, "speed_preset", text="Preset")
        if props.speed_preset == "CUSTOM":
            col.prop(props, "global_speed_multiplier", text="Custom Speed")
        
        # Timing control
        box = layout.box()
        box.label(text="Timing Control", icon='KEYFRAME')
        
        col = box.column()
        col.prop(props, "timing_offset")
        
        # Loop and reverse
        box = layout.box()
        box.label(text="Loop & Reverse", icon='FILE_REFRESH')
        
        col = box.column()
        col.prop(props, "loop_enabled")
        if props.loop_enabled:
            col.prop(props, "loop_count")
        col.prop(props, "reverse_enabled")
        
        # Blend settings
        box = layout.box()
        box.label(text="Blend & Influence", icon='MODIFIER')
        
        col = box.column()
        col.prop(props, "blend_in_frames")
        col.prop(props, "blend_out_frames")
    
    def draw_creator_tab(self, context, layout):
        """Draw Preset Creator tab content"""
        props = context.scene.rigaddon_play_props
        
        # Custom presets list
        box = layout.box()
        box.label(text="Custom Presets", icon='PRESET')
        
        # New preset settings
        box = layout.box()
        box.label(text="Create New Preset", icon='ADD')
        
        col = box.column()
        col.prop(props, "new_preset_name")
        col.prop(props, "new_preset_category")
        col.prop(props, "new_preset_duration")
        col.prop(props, "new_preset_loopable")
        
        # Capture methods
        row = box.row()
        row.operator("rigaddon.capture_keyframes_rgp", text="Capture Keyframes", icon='KEYFRAME_HLT')
        
        # Recording
        box = layout.box()
        box.label(text="Record Realtime", icon='REC')
        
        col = box.column()
        col.prop(props, "record_start_frame")
        col.prop(props, "record_end_frame")
        
        if props.is_recording:
            col.operator("rigaddon.stop_recording_rgp", text="Stop Recording", icon='PAUSE')
        else:
            col.operator("rigaddon.start_recording_rgp", text="Start Recording", icon='REC')
        
        # Export/Import
        box = layout.box()
        box.label(text="Export / Import", icon='IMPORT')
        
        row = box.row()
        row.operator("rigaddon.export_presets_rgp", text="Export", icon='EXPORT')
        row.operator("rigaddon.import_presets_rgp", text="Import", icon='IMPORT')
    
    def draw_help_tab(self, context, layout):
        """Draw Help/Info tab content"""
        box = layout.box()
        box.label(text="Rigaddon Play Help", icon='HELP')
        
        col = box.column()
        col.label(text="Quick Start:")
        col.label(text="1. Select object(s)")
        col.label(text="2. Choose preset from Presets tab")
        col.label(text="3. Click Apply or use Button mode")
        col.label(text="4. Use Animation Control for fine-tuning")
        
        col.separator()
        col.operator("rigaddon.show_tutorial_rgp", text="Show Tutorial", icon='QUESTION')
        col.operator("rigaddon.reset_default_presets_rgp", text="Reset Default Presets", icon='FILE_REFRESH')
    
    def draw_preset_info(self, context, layout):
        """Draw preset statistics info"""
        from . import preset_manager_rgp
        
        box = layout.box()
        box.label(text="Preset Info", icon='INFO')
        
        stats = preset_manager_rgp.get_preset_statistics_rgp()
        
        col = box.column()
        col.label(text=f"Total Presets: {stats['total']}")
        col.label(text=f"Default: {stats['default']} | Custom: {stats['custom']}")
        col.label(text=f"Favorites: {stats['favorites']}")

    def draw_playback_control(self, context, layout):
        """Draw playback control section (always visible)"""
        props = context.scene.rigaddon_play_props

        box = layout.box()
        box.label(text="Playback Control", icon='PLAY')

        # Playback mode
        row = box.row()
        row.prop(props, "playback_mode", text="")

        # Range settings for IN_RANGE mode
        if props.playback_mode == "IN_RANGE":
            row = box.row()
            row.prop(props, "playback_range_start", text="Start")
            row.prop(props, "playback_range_end", text="End")

        # Playback buttons
        row = box.row(align=True)

        if props.is_playing:
            row.operator("rigaddon.stop_playback_rgp", text="Stop", icon='SNAP_FACE')
            row.operator("rigaddon.pause_playback_rgp", text="Pause", icon='PAUSE')
        else:
            row.operator("rigaddon.play_once_rgp", text="Play Once", icon='PLAY')
            row.operator("rigaddon.play_loop_rgp", text="Loop", icon='FILE_REFRESH')

        row = box.row(align=True)
        row.operator("rigaddon.play_from_here_rgp", text="From Here", icon='PLAY_REVERSE')

        if props.playback_mode == "IN_RANGE":
            row.operator("rigaddon.play_in_range_rgp", text="In Range", icon='PREVIEW_RANGE')

# UIList classes
class RIGADDON_UL_presets_rgp(UIList):
    """UIList for presets"""
    
    def draw_item(self, context, layout, data, item, icon, active_data, active_propname):
        if self.layout_type in {'DEFAULT', 'COMPACT'}:
            row = layout.row(align=True)
            row.label(text=item.name, icon='PRESET')
            row.label(text=item.category)
            row.label(text=f"{item.duration}f")
            
            # Favorite icon
            fav_icon = 'SOLO_ON' if item.favorite else 'SOLO_OFF'
            row.label(text="", icon=fav_icon)

class RIGADDON_UL_active_presets_rgp(UIList):
    """UIList for active presets"""
    
    def draw_item(self, context, layout, data, item, icon, active_data, active_propname):
        if self.layout_type in {'DEFAULT', 'COMPACT'}:
            row = layout.row(align=True)
            row.label(text=item.name, icon='PLAY')
            
            # Status icons
            if item.is_active:
                row.label(text="", icon='RADIOBUT_ON')
            else:
                row.label(text="", icon='RADIOBUT_OFF')

class RIGADDON_UL_animation_strips_rgp(UIList):
    """UIList for animation strips"""
    
    def draw_item(self, context, layout, data, item, icon, active_data, active_propname):
        if self.layout_type in {'DEFAULT', 'COMPACT'}:
            row = layout.row(align=True)
            row.label(text=item.name, icon='NLA')
            row.label(text=f"{item.start_frame}-{item.end_frame}")
            row.label(text=f"{item.speed_multiplier:.1f}x")
            
            # Status
            if item.is_muted:
                row.label(text="", icon='HIDE_ON')
            elif item.is_solo:
                row.label(text="", icon='SOLO_ON')
            else:
                row.label(text="", icon='HIDE_OFF')

def register():
    """Register UI classes"""
    bpy.utils.register_class(RIGADDON_UL_presets_rgp)
    bpy.utils.register_class(RIGADDON_UL_active_presets_rgp)
    bpy.utils.register_class(RIGADDON_UL_animation_strips_rgp)
    bpy.utils.register_class(RIGADDON_PT_main_panel_rgp)

def unregister():
    """Unregister UI classes"""
    bpy.utils.unregister_class(RIGADDON_PT_main_panel_rgp)
    bpy.utils.unregister_class(RIGADDON_UL_animation_strips_rgp)
    bpy.utils.unregister_class(RIGADDON_UL_active_presets_rgp)
    bpy.utils.unregister_class(RIGADDON_UL_presets_rgp)
