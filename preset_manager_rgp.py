"""
Rigaddon Play - Preset Manager <PERSON><PERSON><PERSON>
Handles loading, saving, and managing motion presets
"""

import bpy
import os
import json
from typing import Dict, List, Any, Optional

# Global preset storage
preset_data_rgp = {
    "default_presets": [],
    "custom_presets": [],
    "categories": set(),
    "favorites_count": 0
}

def get_addon_directory_rgp() -> str:
    """Get the addon directory path"""
    return os.path.dirname(os.path.realpath(__file__))

def get_default_presets_path_rgp() -> str:
    """Get path to default presets JSON file"""
    return os.path.join(get_addon_directory_rgp(), "default_presets.json")

def get_custom_presets_path_rgp() -> str:
    """Get path to custom presets JSON file"""
    return os.path.join(get_addon_directory_rgp(), "custom_presets.json")

def load_json_file_rgp(filepath: str) -> Dict[str, Any]:
    """Load JSON file safely"""
    try:
        if os.path.exists(filepath):
            with open(filepath, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            print(f"Rigaddon Play: File not found: {filepath}")
            return {"presets": []}
    except Exception as e:
        print(f"Rigaddon Play: Error loading JSON file {filepath}: {e}")
        return {"presets": []}

def save_json_file_rgp(filepath: str, data: Dict[str, Any]) -> bool:
    """Save JSON file safely"""
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        return True
    except Exception as e:
        print(f"Rigaddon Play: Error saving JSON file {filepath}: {e}")
        return False

def load_default_presets_rgp() -> List[Dict[str, Any]]:
    """Load default presets from JSON file"""
    data = load_json_file_rgp(get_default_presets_path_rgp())
    return data.get("presets", [])

def load_custom_presets_rgp() -> List[Dict[str, Any]]:
    """Load custom presets from JSON file"""
    data = load_json_file_rgp(get_custom_presets_path_rgp())
    return data.get("presets", [])

def save_custom_presets_rgp(presets: List[Dict[str, Any]]) -> bool:
    """Save custom presets to JSON file"""
    data = {"presets": presets}
    return save_json_file_rgp(get_custom_presets_path_rgp(), data)

def initialize_presets_rgp():
    """Initialize preset system - load all presets"""
    global preset_data_rgp
    
    try:
        # Load default presets
        preset_data_rgp["default_presets"] = load_default_presets_rgp()
        
        # Load custom presets
        preset_data_rgp["custom_presets"] = load_custom_presets_rgp()
        
        # Update categories and favorites count
        update_preset_statistics_rgp()
        
        print(f"Rigaddon Play: Loaded {len(preset_data_rgp['default_presets'])} default presets")
        print(f"Rigaddon Play: Loaded {len(preset_data_rgp['custom_presets'])} custom presets")
        
    except Exception as e:
        print(f"Rigaddon Play: Error initializing presets: {e}")

def update_preset_statistics_rgp():
    """Update preset statistics (categories, favorites count)"""
    global preset_data_rgp
    
    categories = set()
    favorites_count = 0
    
    # Process default presets
    for preset in preset_data_rgp["default_presets"]:
        if "category" in preset:
            categories.add(preset["category"])
        if preset.get("favorite", False):
            favorites_count += 1
    
    # Process custom presets
    for preset in preset_data_rgp["custom_presets"]:
        if "category" in preset:
            categories.add(preset["category"])
        if preset.get("favorite", False):
            favorites_count += 1
    
    preset_data_rgp["categories"] = categories
    preset_data_rgp["favorites_count"] = favorites_count

def get_all_presets_rgp() -> List[Dict[str, Any]]:
    """Get all presets (default + custom)"""
    return preset_data_rgp["default_presets"] + preset_data_rgp["custom_presets"]

def get_presets_by_category_rgp(category: str) -> List[Dict[str, Any]]:
    """Get presets filtered by category"""
    all_presets = get_all_presets_rgp()
    return [preset for preset in all_presets if preset.get("category") == category]

def get_favorite_presets_rgp() -> List[Dict[str, Any]]:
    """Get all favorite presets"""
    all_presets = get_all_presets_rgp()
    return [preset for preset in all_presets if preset.get("favorite", False)]

def get_preset_categories_rgp() -> List[str]:
    """Get list of all categories"""
    return sorted(list(preset_data_rgp["categories"]))

def get_preset_statistics_rgp() -> Dict[str, int]:
    """Get preset statistics"""
    stats = {
        "total": len(get_all_presets_rgp()),
        "default": len(preset_data_rgp["default_presets"]),
        "custom": len(preset_data_rgp["custom_presets"]),
        "favorites": preset_data_rgp["favorites_count"]
    }
    
    # Count by category
    for category in preset_data_rgp["categories"]:
        stats[f"category_{category}"] = len(get_presets_by_category_rgp(category))
    
    return stats

def add_custom_preset_rgp(preset: Dict[str, Any]) -> bool:
    """Add a new custom preset"""
    try:
        preset_data_rgp["custom_presets"].append(preset)
        success = save_custom_presets_rgp(preset_data_rgp["custom_presets"])
        if success:
            update_preset_statistics_rgp()
        return success
    except Exception as e:
        print(f"Rigaddon Play: Error adding custom preset: {e}")
        return False

def remove_custom_preset_rgp(preset_name: str) -> bool:
    """Remove a custom preset by name"""
    try:
        original_count = len(preset_data_rgp["custom_presets"])
        preset_data_rgp["custom_presets"] = [
            p for p in preset_data_rgp["custom_presets"] 
            if p.get("name") != preset_name
        ]
        
        if len(preset_data_rgp["custom_presets"]) < original_count:
            success = save_custom_presets_rgp(preset_data_rgp["custom_presets"])
            if success:
                update_preset_statistics_rgp()
            return success
        return False
    except Exception as e:
        print(f"Rigaddon Play: Error removing custom preset: {e}")
        return False

def toggle_preset_favorite_rgp(preset_name: str, is_custom: bool = False) -> bool:
    """Toggle favorite status of a preset"""
    try:
        preset_list = preset_data_rgp["custom_presets"] if is_custom else preset_data_rgp["default_presets"]
        
        for preset in preset_list:
            if preset.get("name") == preset_name:
                preset["favorite"] = not preset.get("favorite", False)
                
                # Save if custom preset
                if is_custom:
                    success = save_custom_presets_rgp(preset_data_rgp["custom_presets"])
                else:
                    # For default presets, we don't save to file, just keep in memory
                    success = True
                
                if success:
                    update_preset_statistics_rgp()
                return success
        
        return False
    except Exception as e:
        print(f"Rigaddon Play: Error toggling preset favorite: {e}")
        return False

def find_preset_by_name_rgp(preset_name: str) -> Optional[Dict[str, Any]]:
    """Find a preset by name"""
    all_presets = get_all_presets_rgp()
    for preset in all_presets:
        if preset.get("name") == preset_name:
            return preset
    return None

def reset_default_presets_rgp() -> bool:
    """Reset default presets to original state (reload from file)"""
    try:
        # Reset favorites in default presets
        for preset in preset_data_rgp["default_presets"]:
            preset["favorite"] = False
        
        # Reload from file
        preset_data_rgp["default_presets"] = load_default_presets_rgp()
        update_preset_statistics_rgp()
        
        print("Rigaddon Play: Default presets reset successfully")
        return True
    except Exception as e:
        print(f"Rigaddon Play: Error resetting default presets: {e}")
        return False

def sort_presets_rgp(presets: List[Dict[str, Any]], sort_type: str) -> List[Dict[str, Any]]:
    """Sort presets by specified criteria"""
    try:
        if sort_type == "NAME_AZ":
            return sorted(presets, key=lambda x: x.get("name", "").lower())
        elif sort_type == "NAME_ZA":
            return sorted(presets, key=lambda x: x.get("name", "").lower(), reverse=True)
        elif sort_type == "DURATION_SHORT":
            return sorted(presets, key=lambda x: x.get("duration", 0))
        elif sort_type == "DURATION_LONG":
            return sorted(presets, key=lambda x: x.get("duration", 0), reverse=True)
        else:
            return presets
    except Exception as e:
        print(f"Rigaddon Play: Error sorting presets: {e}")
        return presets

def filter_presets_rgp(presets: List[Dict[str, Any]], category: str = "", favorites_only: bool = False) -> List[Dict[str, Any]]:
    """Filter presets by category and/or favorites"""
    try:
        filtered = presets
        
        if category and category != "ALL":
            filtered = [p for p in filtered if p.get("category") == category]
        
        if favorites_only:
            filtered = [p for p in filtered if p.get("favorite", False)]
        
        return filtered
    except Exception as e:
        print(f"Rigaddon Play: Error filtering presets: {e}")
        return presets
