"""
Rigaddon Play - Operators Module
Defines all operators for the addon functionality
"""

import bpy
import bmesh
from bpy.types import Operator
from bpy.props import StringProperty, BoolProperty, IntProperty, FloatProperty
import mathutils
import random

class RIGADDON_OT_apply_preset_rgp(Operator):
    """Apply selected preset to active objects"""
    bl_idname = "rigaddon.apply_preset_rgp"
    bl_label = "Apply Preset"
    bl_description = "Apply the selected preset to active objects"
    bl_options = {'REGISTER', 'UNDO'}
    
    def execute(self, context):
        props = context.scene.rigaddon_play_props
        
        if not context.selected_objects:
            self.report({'WARNING'}, "No objects selected")
            return {'CANCELLED'}
        
        if props.preset_list_index >= len(props.preset_list):
            self.report({'WARNING'}, "No preset selected")
            return {'CANCELLED'}
        
        preset = props.preset_list[props.preset_list_index]
        return self.apply_preset_to_objects(context, preset.name, context.selected_objects)
    
    def apply_preset_to_objects(self, context, preset_name, objects):
        """Apply preset to specified objects"""
        from . import preset_manager_rgp
        
        preset_data = preset_manager_rgp.find_preset_by_name_rgp(preset_name)
        if not preset_data:
            self.report({'ERROR'}, f"Preset '{preset_name}' not found")
            return {'CANCELLED'}
        
        props = context.scene.rigaddon_play_props
        
        for i, obj in enumerate(objects):
            # Calculate delay for multi-object
            delay = 0
            if props.random_delay_enabled and len(objects) > 1:
                delay = random.randint(0, props.random_delay_max)
            
            # Apply keyframes
            self.apply_keyframes_to_object(obj, preset_data, delay)
        
        # Add to active presets list
        self.add_to_active_presets(context, preset_name)
        
        self.report({'INFO'}, f"Applied preset '{preset_name}' to {len(objects)} object(s)")
        return {'FINISHED'}
    
    def apply_keyframes_to_object(self, obj, preset_data, delay=0):
        """Apply keyframes from preset to object"""
        keyframes = preset_data.get("keyframes", {})
        
        # Clear existing animation data if in replace mode
        if obj.animation_data:
            obj.animation_data_clear()
        
        # Ensure animation data exists
        if not obj.animation_data:
            obj.animation_data_create()
        
        # Create action
        action = bpy.data.actions.new(name=f"{obj.name}_{preset_data['name']}")
        obj.animation_data.action = action
        
        # Apply location keyframes
        if "location" in keyframes:
            self.apply_transform_keyframes(obj, "location", keyframes["location"], delay)
        
        # Apply rotation keyframes
        if "rotation" in keyframes:
            self.apply_transform_keyframes(obj, "rotation_euler", keyframes["rotation"], delay)
        
        # Apply scale keyframes
        if "scale" in keyframes:
            self.apply_transform_keyframes(obj, "scale", keyframes["scale"], delay)
    
    def apply_transform_keyframes(self, obj, data_path, keyframe_data, delay):
        """Apply transform keyframes to object"""
        for keyframe in keyframe_data:
            frame = keyframe["frame"] + delay
            value = keyframe["value"]
            
            # Set the value
            if data_path == "location":
                obj.location = value
            elif data_path == "rotation_euler":
                obj.rotation_euler = [mathutils.radians(v) for v in value]
            elif data_path == "scale":
                obj.scale = value
            
            # Insert keyframe
            obj.keyframe_insert(data_path=data_path, frame=frame)
    
    def add_to_active_presets(self, context, preset_name):
        """Add preset to active presets list"""
        props = context.scene.rigaddon_play_props
        
        # Check if already in list
        for item in props.active_preset_list:
            if item.name == preset_name:
                return
        
        # Add new item
        item = props.active_preset_list.add()
        item.name = preset_name
        item.is_active = True

class RIGADDON_OT_apply_preset_by_name_rgp(Operator):
    """Apply preset by name (for button mode)"""
    bl_idname = "rigaddon.apply_preset_by_name_rgp"
    bl_label = "Apply Preset"
    bl_description = "Apply preset by name"
    bl_options = {'REGISTER', 'UNDO'}
    
    preset_name: StringProperty(name="Preset Name")
    
    def execute(self, context):
        if not context.selected_objects:
            self.report({'WARNING'}, "No objects selected")
            return {'CANCELLED'}
        
        apply_op = RIGADDON_OT_apply_preset_rgp()
        return apply_op.apply_preset_to_objects(context, self.preset_name, context.selected_objects)

class RIGADDON_OT_toggle_favorite_rgp(Operator):
    """Toggle favorite status of preset"""
    bl_idname = "rigaddon.toggle_favorite_rgp"
    bl_label = "Toggle Favorite"
    bl_description = "Toggle favorite status of the selected preset"
    bl_options = {'REGISTER', 'UNDO'}
    
    def execute(self, context):
        props = context.scene.rigaddon_play_props
        
        if props.preset_list_index >= len(props.preset_list):
            self.report({'WARNING'}, "No preset selected")
            return {'CANCELLED'}
        
        preset = props.preset_list[props.preset_list_index]
        
        from . import preset_manager_rgp
        success = preset_manager_rgp.toggle_preset_favorite_rgp(preset.name, preset.is_custom)
        
        if success:
            preset.favorite = not preset.favorite
            self.report({'INFO'}, f"Toggled favorite for '{preset.name}'")
            return {'FINISHED'}
        else:
            self.report({'ERROR'}, "Failed to toggle favorite")
            return {'CANCELLED'}

class RIGADDON_OT_delete_custom_preset_rgp(Operator):
    """Delete custom preset"""
    bl_idname = "rigaddon.delete_custom_preset_rgp"
    bl_label = "Delete Custom Preset"
    bl_description = "Delete the selected custom preset"
    bl_options = {'REGISTER', 'UNDO'}
    
    def execute(self, context):
        props = context.scene.rigaddon_play_props
        
        if props.preset_list_index >= len(props.preset_list):
            self.report({'WARNING'}, "No preset selected")
            return {'CANCELLED'}
        
        preset = props.preset_list[props.preset_list_index]
        
        if not preset.is_custom:
            self.report({'WARNING'}, "Cannot delete default preset")
            return {'CANCELLED'}
        
        from . import preset_manager_rgp
        success = preset_manager_rgp.remove_custom_preset_rgp(preset.name)
        
        if success:
            # Remove from UI list
            props.preset_list.remove(props.preset_list_index)
            if props.preset_list_index > 0:
                props.preset_list_index -= 1
            
            self.report({'INFO'}, f"Deleted preset '{preset.name}'")
            return {'FINISHED'}
        else:
            self.report({'ERROR'}, "Failed to delete preset")
            return {'CANCELLED'}

class RIGADDON_OT_remove_active_preset_rgp(Operator):
    """Remove preset from active list"""
    bl_idname = "rigaddon.remove_active_preset_rgp"
    bl_label = "Remove Active Preset"
    bl_description = "Remove preset from active list"
    bl_options = {'REGISTER', 'UNDO'}
    
    def execute(self, context):
        props = context.scene.rigaddon_play_props
        
        if props.active_preset_index >= len(props.active_preset_list):
            self.report({'WARNING'}, "No active preset selected")
            return {'CANCELLED'}
        
        preset_name = props.active_preset_list[props.active_preset_index].name
        props.active_preset_list.remove(props.active_preset_index)
        
        if props.active_preset_index > 0:
            props.active_preset_index -= 1
        
        self.report({'INFO'}, f"Removed '{preset_name}' from active presets")
        return {'FINISHED'}

class RIGADDON_OT_reset_default_presets_rgp(Operator):
    """Reset default presets to original state"""
    bl_idname = "rigaddon.reset_default_presets_rgp"
    bl_label = "Reset Default Presets"
    bl_description = "Reset default presets to original state"
    bl_options = {'REGISTER', 'UNDO'}
    
    def execute(self, context):
        from . import preset_manager_rgp
        
        success = preset_manager_rgp.reset_default_presets_rgp()
        
        if success:
            # Refresh UI list
            self.refresh_preset_list(context)
            self.report({'INFO'}, "Default presets reset successfully")
            return {'FINISHED'}
        else:
            self.report({'ERROR'}, "Failed to reset default presets")
            return {'CANCELLED'}
    
    def refresh_preset_list(self, context):
        """Refresh the preset list in UI"""
        props = context.scene.rigaddon_play_props
        
        # Clear current list
        props.preset_list.clear()
        
        # Reload from preset manager
        from . import preset_manager_rgp
        all_presets = preset_manager_rgp.get_all_presets_rgp()
        
        for preset_data in all_presets:
            item = props.preset_list.add()
            item.name = preset_data.get("name", "")
            item.category = preset_data.get("category", "")
            item.duration = preset_data.get("duration", 30)
            item.loopable = preset_data.get("loopable", True)
            item.favorite = preset_data.get("favorite", False)
            item.is_custom = preset_data in preset_manager_rgp.preset_data_rgp["custom_presets"]

class RIGADDON_OT_refresh_presets_rgp(Operator):
    """Refresh preset list"""
    bl_idname = "rigaddon.refresh_presets_rgp"
    bl_label = "Refresh Presets"
    bl_description = "Refresh the preset list"
    bl_options = {'REGISTER'}

    def execute(self, context):
        self.refresh_preset_list(context)
        return {'FINISHED'}

    def refresh_preset_list(self, context):
        """Refresh the preset list in UI"""
        props = context.scene.rigaddon_play_props

        # Clear current list
        props.preset_list.clear()

        # Reload from preset manager
        from . import preset_manager_rgp
        all_presets = preset_manager_rgp.get_all_presets_rgp()

        # Apply current filters and sorting
        filtered_presets = preset_manager_rgp.filter_presets_rgp(
            all_presets,
            props.category_filter if props.category_filter != "ALL" else "",
            props.favorites_only
        )

        sorted_presets = preset_manager_rgp.sort_presets_rgp(filtered_presets, props.sort_type)

        # Populate UI list
        for preset_data in sorted_presets:
            item = props.preset_list.add()
            item.name = preset_data.get("name", "")
            item.category = preset_data.get("category", "")
            item.duration = preset_data.get("duration", 30)
            item.loopable = preset_data.get("loopable", True)
            item.favorite = preset_data.get("favorite", False)
            item.is_custom = preset_data in preset_manager_rgp.preset_data_rgp["custom_presets"]

class RIGADDON_OT_capture_keyframes_rgp(Operator):
    """Capture keyframes from selected objects to create preset"""
    bl_idname = "rigaddon.capture_keyframes_rgp"
    bl_label = "Capture Keyframes"
    bl_description = "Capture keyframes from selected objects to create a new preset"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        props = context.scene.rigaddon_play_props

        if not context.selected_objects:
            self.report({'WARNING'}, "No objects selected")
            return {'CANCELLED'}

        if not props.new_preset_name:
            self.report({'WARNING'}, "Please enter a preset name")
            return {'CANCELLED'}

        # Capture keyframes from first selected object
        obj = context.selected_objects[0]

        if not obj.animation_data or not obj.animation_data.action:
            self.report({'WARNING'}, "Selected object has no animation data")
            return {'CANCELLED'}

        # Extract keyframes
        keyframes_data = self.extract_keyframes_from_object(obj)

        # Create preset data
        preset_data = {
            "name": props.new_preset_name,
            "category": props.new_preset_category,
            "duration": props.new_preset_duration,
            "loopable": props.new_preset_loopable,
            "favorite": False,
            "keyframes": keyframes_data
        }

        # Save to custom presets
        from . import preset_manager_rgp
        success = preset_manager_rgp.add_custom_preset_rgp(preset_data)

        if success:
            # Refresh UI
            refresh_op = RIGADDON_OT_refresh_presets_rgp()
            refresh_op.execute(context)

            self.report({'INFO'}, f"Created preset '{props.new_preset_name}'")
            return {'FINISHED'}
        else:
            self.report({'ERROR'}, "Failed to save preset")
            return {'CANCELLED'}

    def extract_keyframes_from_object(self, obj):
        """Extract keyframes from object's animation data"""
        keyframes_data = {}

        if not obj.animation_data or not obj.animation_data.action:
            return keyframes_data

        action = obj.animation_data.action

        # Extract location keyframes
        location_curves = [fc for fc in action.fcurves if fc.data_path == "location"]
        if location_curves:
            keyframes_data["location"] = self.extract_curve_keyframes(location_curves)

        # Extract rotation keyframes
        rotation_curves = [fc for fc in action.fcurves if fc.data_path == "rotation_euler"]
        if rotation_curves:
            keyframes_data["rotation"] = self.extract_curve_keyframes(rotation_curves, convert_to_degrees=True)

        # Extract scale keyframes
        scale_curves = [fc for fc in action.fcurves if fc.data_path == "scale"]
        if scale_curves:
            keyframes_data["scale"] = self.extract_curve_keyframes(scale_curves)

        return keyframes_data

    def extract_curve_keyframes(self, curves, convert_to_degrees=False):
        """Extract keyframes from F-curves"""
        keyframes = []

        # Get all unique frame numbers
        frames = set()
        for curve in curves:
            for keyframe in curve.keyframe_points:
                frames.add(int(keyframe.co[0]))

        # Extract values for each frame
        for frame in sorted(frames):
            values = [0, 0, 0]  # Default for 3D vectors

            for curve in curves:
                if curve.array_index < len(values):
                    value = curve.evaluate(frame)
                    if convert_to_degrees:
                        value = mathutils.degrees(value)
                    values[curve.array_index] = value

            keyframes.append({
                "frame": frame,
                "value": values
            })

        return keyframes

class RIGADDON_OT_start_recording_rgp(Operator):
    """Start recording realtime animation"""
    bl_idname = "rigaddon.start_recording_rgp"
    bl_label = "Start Recording"
    bl_description = "Start recording realtime animation"
    bl_options = {'REGISTER'}

    def execute(self, context):
        props = context.scene.rigaddon_play_props

        if not context.selected_objects:
            self.report({'WARNING'}, "No objects selected")
            return {'CANCELLED'}

        props.is_recording = True

        # Set up recording
        scene = context.scene
        scene.frame_current = props.record_start_frame

        self.report({'INFO'}, "Recording started - move objects and play timeline")
        return {'FINISHED'}

class RIGADDON_OT_stop_recording_rgp(Operator):
    """Stop recording and create preset"""
    bl_idname = "rigaddon.stop_recording_rgp"
    bl_label = "Stop Recording"
    bl_description = "Stop recording and create preset from recorded data"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        props = context.scene.rigaddon_play_props

        props.is_recording = False

        # This would need more complex implementation to capture realtime data
        self.report({'INFO'}, "Recording stopped")
        return {'FINISHED'}

class RIGADDON_OT_export_presets_rgp(Operator):
    """Export custom presets to file"""
    bl_idname = "rigaddon.export_presets_rgp"
    bl_label = "Export Presets"
    bl_description = "Export custom presets to JSON file"
    bl_options = {'REGISTER'}

    def execute(self, context):
        # This would open file browser for export
        self.report({'INFO'}, "Export functionality would open file browser")
        return {'FINISHED'}

class RIGADDON_OT_import_presets_rgp(Operator):
    """Import presets from file"""
    bl_idname = "rigaddon.import_presets_rgp"
    bl_label = "Import Presets"
    bl_description = "Import presets from JSON file"
    bl_options = {'REGISTER'}

    def execute(self, context):
        # This would open file browser for import
        self.report({'INFO'}, "Import functionality would open file browser")
        return {'FINISHED'}

class RIGADDON_OT_show_tutorial_rgp(Operator):
    """Show tutorial popup"""
    bl_idname = "rigaddon.show_tutorial_rgp"
    bl_label = "Show Tutorial"
    bl_description = "Show tutorial information"
    bl_options = {'REGISTER'}

    def execute(self, context):
        def draw_tutorial(self, context):
            layout = self.layout
            layout.label(text="Rigaddon Play Tutorial")
            layout.separator()
            layout.label(text="1. Select object(s) you want to animate")
            layout.label(text="2. Go to Presets tab")
            layout.label(text="3. Choose a preset from the library")
            layout.label(text="4. Click Apply or use Button mode")
            layout.label(text="5. Use Animation Control for fine-tuning")
            layout.separator()
            layout.label(text="Tips:")
            layout.label(text="• Use Random Delay for multi-object animations")
            layout.label(text="• Mark presets as favorites for quick access")
            layout.label(text="• Create custom presets in Preset Creator tab")

        context.window_manager.popup_menu(draw_tutorial, title="Tutorial", icon='HELP')
        return {'FINISHED'}

class RIGADDON_OT_initialize_presets_rgp(Operator):
    """Initialize preset system and UI"""
    bl_idname = "rigaddon.initialize_presets_rgp"
    bl_label = "Initialize Presets"
    bl_description = "Initialize preset system and populate UI"
    bl_options = {'REGISTER'}

    def execute(self, context):
        try:
            # Initialize preset manager
            from . import preset_manager_rgp
            preset_manager_rgp.initialize_presets_rgp()

            # Refresh UI
            refresh_op = RIGADDON_OT_refresh_presets_rgp()
            refresh_op.refresh_preset_list(context)

            self.report({'INFO'}, f"Initialized {len(preset_manager_rgp.get_all_presets_rgp())} presets")
            return {'FINISHED'}
        except Exception as e:
            self.report({'ERROR'}, f"Failed to initialize presets: {e}")
            return {'CANCELLED'}

def register():
    """Register operators"""
    bpy.utils.register_class(RIGADDON_OT_apply_preset_rgp)
    bpy.utils.register_class(RIGADDON_OT_apply_preset_by_name_rgp)
    bpy.utils.register_class(RIGADDON_OT_toggle_favorite_rgp)
    bpy.utils.register_class(RIGADDON_OT_delete_custom_preset_rgp)
    bpy.utils.register_class(RIGADDON_OT_remove_active_preset_rgp)
    bpy.utils.register_class(RIGADDON_OT_reset_default_presets_rgp)
    bpy.utils.register_class(RIGADDON_OT_refresh_presets_rgp)
    bpy.utils.register_class(RIGADDON_OT_capture_keyframes_rgp)
    bpy.utils.register_class(RIGADDON_OT_start_recording_rgp)
    bpy.utils.register_class(RIGADDON_OT_stop_recording_rgp)
    bpy.utils.register_class(RIGADDON_OT_export_presets_rgp)
    bpy.utils.register_class(RIGADDON_OT_import_presets_rgp)
    bpy.utils.register_class(RIGADDON_OT_show_tutorial_rgp)
    bpy.utils.register_class(RIGADDON_OT_initialize_presets_rgp)

def unregister():
    """Unregister operators"""
    bpy.utils.unregister_class(RIGADDON_OT_initialize_presets_rgp)
    bpy.utils.unregister_class(RIGADDON_OT_show_tutorial_rgp)
    bpy.utils.unregister_class(RIGADDON_OT_import_presets_rgp)
    bpy.utils.unregister_class(RIGADDON_OT_export_presets_rgp)
    bpy.utils.unregister_class(RIGADDON_OT_stop_recording_rgp)
    bpy.utils.unregister_class(RIGADDON_OT_start_recording_rgp)
    bpy.utils.unregister_class(RIGADDON_OT_capture_keyframes_rgp)
    bpy.utils.unregister_class(RIGADDON_OT_refresh_presets_rgp)
    bpy.utils.unregister_class(RIGADDON_OT_reset_default_presets_rgp)
    bpy.utils.unregister_class(RIGADDON_OT_remove_active_preset_rgp)
    bpy.utils.unregister_class(RIGADDON_OT_delete_custom_preset_rgp)
    bpy.utils.unregister_class(RIGADDON_OT_toggle_favorite_rgp)
    bpy.utils.unregister_class(RIGADDON_OT_apply_preset_by_name_rgp)
    bpy.utils.unregister_class(RIGADDON_OT_apply_preset_rgp)
