"""
Rigaddon Play - Properties Module
Defines all Blender properties for the addon
"""

import bpy
from bpy.types import PropertyGroup
from bpy.props import (
    StringProperty, 
    IntProperty, 
    FloatProperty, 
    BoolProperty, 
    EnumProperty,
    CollectionProperty,
    PointerProperty
)

def update_preset_filter_rgp(self, context):
    """Update callback for preset filters"""
    try:
        bpy.ops.rigaddon.refresh_presets_rgp()
    except:
        pass  # Operator might not be available during registration

def get_category_items_rgp(self, context):
    """Dynamic enum items for categories"""
    try:
        from . import preset_manager_rgp

        items = [("ALL", "All Categories", "Show all categories")]
        categories = preset_manager_rgp.get_preset_categories_rgp()

        for category in categories:
            items.append((category, category, f"Show {category} presets"))

        return items
    except:
        return [("ALL", "All Categories", "Show all categories")]

def get_sort_items_rgp(self, context):
    """Enum items for sorting"""
    return [
        ("NAME_AZ", "Name A-Z", "Sort by name A to Z"),
        ("NAME_ZA", "Name Z-A", "Sort by name Z to A"),
        ("DURATION_SHORT", "Duration ↑", "Sort by duration shortest first"),
        ("DURATION_LONG", "Duration ↓", "Sort by duration longest first")
    ]

def get_ui_mode_items_rgp(self, context):
    """Enum items for UI mode"""
    return [
        ("UILIST", "UIList Mode", "Display presets in a list"),
        ("BUTTON", "Button Mode", "Display presets as buttons")
    ]

def get_blend_mode_items_rgp(self, context):
    """Enum items for blend modes"""
    return [
        ("REPLACE", "Replace", "Replace existing animation"),
        ("ADD", "Add", "Add to existing animation"),
        ("MULTIPLY", "Multiply", "Multiply with existing animation")
    ]

def get_playback_mode_items_rgp(self, context):
    """Enum items for playback modes"""
    return [
        ("ONCE", "Play Once", "Play animation once"),
        ("LOOP", "Loop", "Loop animation continuously"),
        ("FROM_HERE", "Play From Here", "Play from current frame"),
        ("IN_RANGE", "Play In Range", "Play in specified range")
    ]

class RigaddonPresetItem_RGP(PropertyGroup):
    """Property group for individual preset items"""
    name: StringProperty(name="Name", default="")
    category: StringProperty(name="Category", default="")
    duration: IntProperty(name="Duration", default=30, min=1)
    loopable: BoolProperty(name="Loopable", default=True)
    favorite: BoolProperty(name="Favorite", default=False)
    is_custom: BoolProperty(name="Is Custom", default=False)
    is_active: BoolProperty(name="Is Active", default=False)

class RigaddonAnimationStrip_RGP(PropertyGroup):
    """Property group for animation strips"""
    name: StringProperty(name="Name", default="")
    start_frame: IntProperty(name="Start Frame", default=1, min=1)
    end_frame: IntProperty(name="End Frame", default=30, min=1)
    speed_multiplier: FloatProperty(name="Speed", default=1.0, min=0.1, max=10.0)
    is_muted: BoolProperty(name="Muted", default=False)
    is_solo: BoolProperty(name="Solo", default=False)
    blend_mode: EnumProperty(
        name="Blend Mode",
        items=get_blend_mode_items_rgp,
        default="REPLACE"
    )
    influence: FloatProperty(name="Influence", default=1.0, min=0.0, max=1.0)

class RigaddonPlayProperties_RGP(PropertyGroup):
    """Main property group for Rigaddon Play addon"""
    
    # === PRESETS TAB ===
    # Filter and Sort
    category_filter: EnumProperty(
        name="Category Filter",
        items=get_category_items_rgp,
        default=0,
        update=update_preset_filter_rgp
    )

    favorites_only: BoolProperty(
        name="Favorites Only",
        description="Show only favorite presets",
        default=False,
        update=update_preset_filter_rgp
    )

    sort_type: EnumProperty(
        name="Sort",
        items=get_sort_items_rgp,
        default="NAME_AZ",
        update=update_preset_filter_rgp
    )
    
    # UI Mode
    ui_mode: EnumProperty(
        name="UI Mode",
        items=get_ui_mode_items_rgp,
        default="UILIST"
    )
    
    # Preset Collections
    preset_list: CollectionProperty(type=RigaddonPresetItem_RGP)
    preset_list_index: IntProperty(name="Preset Index", default=0)
    
    active_preset_list: CollectionProperty(type=RigaddonPresetItem_RGP)
    active_preset_index: IntProperty(name="Active Preset Index", default=0)
    
    # Apply Motion Settings
    random_delay_enabled: BoolProperty(
        name="Random Delay",
        description="Apply random delay to multi-object animations",
        default=False
    )
    
    random_delay_max: IntProperty(
        name="Max Delay",
        description="Maximum random delay in frames",
        default=10,
        min=0,
        max=100
    )
    
    # Preview Mode
    preview_mode: BoolProperty(
        name="Preview Mode",
        description="Apply preset temporarily for preview",
        default=False
    )
    
    # === ANIMATION CONTROL TAB ===
    # Animation Strips
    animation_strips: CollectionProperty(type=RigaddonAnimationStrip_RGP)
    animation_strip_index: IntProperty(name="Animation Strip Index", default=0)
    
    # Speed Control
    global_speed_multiplier: FloatProperty(
        name="Global Speed",
        description="Override speed for all active animations",
        default=1.0,
        min=0.1,
        max=10.0
    )
    
    speed_preset: EnumProperty(
        name="Speed Preset",
        items=[
            ("0.25", "0.25x", "Quarter speed"),
            ("0.5", "0.5x", "Half speed"),
            ("1.0", "1x", "Normal speed"),
            ("2.0", "2x", "Double speed"),
            ("CUSTOM", "Custom", "Custom speed value")
        ],
        default="1.0"
    )
    
    # Timing Control
    timing_offset: IntProperty(
        name="Timing Offset",
        description="Offset animation start time",
        default=0
    )
    
    # Loop and Reverse
    loop_enabled: BoolProperty(
        name="Loop",
        description="Enable looping",
        default=False
    )
    
    loop_count: IntProperty(
        name="Loop Count",
        description="Number of loops (0 = infinite)",
        default=0,
        min=0
    )
    
    reverse_enabled: BoolProperty(
        name="Reverse",
        description="Reverse animation direction",
        default=False
    )
    
    # Blend Settings
    blend_in_frames: IntProperty(
        name="Blend In",
        description="Blend in duration in frames",
        default=5,
        min=0
    )
    
    blend_out_frames: IntProperty(
        name="Blend Out",
        description="Blend out duration in frames",
        default=5,
        min=0
    )
    
    # === PRESET CREATOR TAB ===
    # Custom Preset Settings
    new_preset_name: StringProperty(
        name="Preset Name",
        description="Name for the new preset",
        default="My Custom Preset"
    )
    
    new_preset_category: StringProperty(
        name="Category",
        description="Category for the new preset",
        default="Custom"
    )
    
    new_preset_duration: IntProperty(
        name="Duration",
        description="Duration of the preset in frames",
        default=30,
        min=1
    )
    
    new_preset_loopable: BoolProperty(
        name="Loopable",
        description="Whether the preset can loop",
        default=True
    )
    
    # Recording Settings
    is_recording: BoolProperty(
        name="Is Recording",
        description="Currently recording animation",
        default=False
    )
    
    record_start_frame: IntProperty(
        name="Record Start",
        description="Start frame for recording",
        default=1
    )
    
    record_end_frame: IntProperty(
        name="Record End",
        description="End frame for recording",
        default=30
    )
    
    # === PLAYBACK CONTROL ===
    playback_mode: EnumProperty(
        name="Playback Mode",
        items=get_playback_mode_items_rgp,
        default="ONCE"
    )
    
    playback_range_start: IntProperty(
        name="Range Start",
        description="Start frame for range playback",
        default=1
    )
    
    playback_range_end: IntProperty(
        name="Range End",
        description="End frame for range playback",
        default=250
    )
    
    is_playing: BoolProperty(
        name="Is Playing",
        description="Animation is currently playing",
        default=False
    )
    
    # === UI STATE ===
    active_tab: EnumProperty(
        name="Active Tab",
        items=[
            ("PRESETS", "Presets", "Preset management tab"),
            ("ANIMATION", "Animation Control", "Animation control tab"),
            ("CREATOR", "Preset Creator", "Preset creation tab"),
            ("HELP", "Help / Info", "Help and information tab")
        ],
        default="PRESETS"
    )
    
    show_advanced: BoolProperty(
        name="Show Advanced",
        description="Show advanced options",
        default=False
    )

def register():
    """Register properties"""
    bpy.utils.register_class(RigaddonPresetItem_RGP)
    bpy.utils.register_class(RigaddonAnimationStrip_RGP)
    bpy.utils.register_class(RigaddonPlayProperties_RGP)
    
    # Add properties to scene
    bpy.types.Scene.rigaddon_play_props = PointerProperty(type=RigaddonPlayProperties_RGP)

def unregister():
    """Unregister properties"""
    # Remove properties from scene
    if hasattr(bpy.types.Scene, 'rigaddon_play_props'):
        del bpy.types.Scene.rigaddon_play_props
    
    bpy.utils.unregister_class(RigaddonPlayProperties_RGP)
    bpy.utils.unregister_class(RigaddonAnimationStrip_RGP)
    bpy.utils.unregister_class(RigaddonPresetItem_RGP)
