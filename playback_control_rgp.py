"""
Rigaddon Play - Playback Control Module
Handles animation playback controls
"""

import bpy
from bpy.types import Operator
from bpy.props import <PERSON><PERSON><PERSON><PERSON><PERSON>, IntProperty

class RIGADDON_OT_play_once_rgp(Operator):
    """Play animation once"""
    bl_idname = "rigaddon.play_once_rgp"
    bl_label = "Play Once"
    bl_description = "Play animation once from start to end"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        scene = context.scene
        props = scene.rigaddon_play_props
        
        # Set playback mode
        props.playback_mode = "ONCE"
        props.is_playing = True
        
        # Set frame range
        scene.frame_start = 1
        scene.frame_end = self.get_max_animation_length(context)
        scene.frame_current = scene.frame_start
        
        # Start playback
        bpy.ops.screen.animation_play()
        
        self.report({'INFO'}, "Playing animation once")
        return {'FINISHED'}
    
    def get_max_animation_length(self, context):
        """Get maximum animation length from active presets"""
        props = context.scene.rigaddon_play_props
        max_length = 250  # Default
        
        for strip in props.animation_strips:
            if strip.end_frame > max_length:
                max_length = strip.end_frame
        
        return max_length

class RIGADDON_OT_play_loop_rgp(Operator):
    """Play animation in loop"""
    bl_idname = "rigaddon.play_loop_rgp"
    bl_label = "Play Loop"
    bl_description = "Play animation in continuous loop"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        scene = context.scene
        props = scene.rigaddon_play_props
        
        # Set playback mode
        props.playback_mode = "LOOP"
        props.is_playing = True
        
        # Enable loop in Blender
        scene.frame_start = 1
        scene.frame_end = self.get_max_animation_length(context)
        scene.frame_current = scene.frame_start
        
        # Start playback
        bpy.ops.screen.animation_play()
        
        self.report({'INFO'}, "Playing animation in loop")
        return {'FINISHED'}
    
    def get_max_animation_length(self, context):
        """Get maximum animation length from active presets"""
        props = context.scene.rigaddon_play_props
        max_length = 250  # Default
        
        for strip in props.animation_strips:
            if strip.end_frame > max_length:
                max_length = strip.end_frame
        
        return max_length

class RIGADDON_OT_play_from_here_rgp(Operator):
    """Play animation from current frame"""
    bl_idname = "rigaddon.play_from_here_rgp"
    bl_label = "Play From Here"
    bl_description = "Play animation from current frame"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        scene = context.scene
        props = scene.rigaddon_play_props
        
        # Set playback mode
        props.playback_mode = "FROM_HERE"
        props.is_playing = True
        
        # Set frame range starting from current
        scene.frame_end = self.get_max_animation_length(context)
        
        # Start playback
        bpy.ops.screen.animation_play()
        
        self.report({'INFO'}, f"Playing from frame {scene.frame_current}")
        return {'FINISHED'}
    
    def get_max_animation_length(self, context):
        """Get maximum animation length from active presets"""
        props = context.scene.rigaddon_play_props
        max_length = 250  # Default
        
        for strip in props.animation_strips:
            if strip.end_frame > max_length:
                max_length = strip.end_frame
        
        return max_length

class RIGADDON_OT_play_in_range_rgp(Operator):
    """Play animation in specified range"""
    bl_idname = "rigaddon.play_in_range_rgp"
    bl_label = "Play In Range"
    bl_description = "Play animation in specified frame range"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        scene = context.scene
        props = scene.rigaddon_play_props
        
        # Set playback mode
        props.playback_mode = "IN_RANGE"
        props.is_playing = True
        
        # Set frame range from properties
        scene.frame_start = props.playback_range_start
        scene.frame_end = props.playback_range_end
        scene.frame_current = scene.frame_start
        
        # Start playback
        bpy.ops.screen.animation_play()
        
        self.report({'INFO'}, f"Playing range {props.playback_range_start}-{props.playback_range_end}")
        return {'FINISHED'}

class RIGADDON_OT_stop_playback_rgp(Operator):
    """Stop animation playback"""
    bl_idname = "rigaddon.stop_playback_rgp"
    bl_label = "Stop"
    bl_description = "Stop animation playback"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        props = context.scene.rigaddon_play_props
        
        # Stop playback
        bpy.ops.screen.animation_cancel(restore_frame=False)
        props.is_playing = False
        
        self.report({'INFO'}, "Stopped playback")
        return {'FINISHED'}

class RIGADDON_OT_pause_playback_rgp(Operator):
    """Pause animation playback"""
    bl_idname = "rigaddon.pause_playback_rgp"
    bl_label = "Pause"
    bl_description = "Pause animation playback"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        props = context.scene.rigaddon_play_props
        
        # Pause playback
        bpy.ops.screen.animation_cancel(restore_frame=False)
        props.is_playing = False
        
        self.report({'INFO'}, "Paused playback")
        return {'FINISHED'}

class RIGADDON_OT_mute_preset_rgp(Operator):
    """Mute/unmute active preset"""
    bl_idname = "rigaddon.mute_preset_rgp"
    bl_label = "Mute Preset"
    bl_description = "Mute or unmute the selected active preset"
    bl_options = {'REGISTER', 'UNDO'}
    
    def execute(self, context):
        props = context.scene.rigaddon_play_props
        
        if props.active_preset_index >= len(props.active_preset_list):
            self.report({'WARNING'}, "No active preset selected")
            return {'CANCELLED'}
        
        preset = props.active_preset_list[props.active_preset_index]
        
        # Toggle mute status (simplified - would need more complex implementation)
        self.report({'INFO'}, f"Toggled mute for '{preset.name}'")
        return {'FINISHED'}

class RIGADDON_OT_solo_preset_rgp(Operator):
    """Solo active preset"""
    bl_idname = "rigaddon.solo_preset_rgp"
    bl_label = "Solo Preset"
    bl_description = "Solo the selected active preset"
    bl_options = {'REGISTER', 'UNDO'}
    
    def execute(self, context):
        props = context.scene.rigaddon_play_props
        
        if props.active_preset_index >= len(props.active_preset_list):
            self.report({'WARNING'}, "No active preset selected")
            return {'CANCELLED'}
        
        preset = props.active_preset_list[props.active_preset_index]
        
        # Toggle solo status (simplified - would need more complex implementation)
        self.report({'INFO'}, f"Toggled solo for '{preset.name}'")
        return {'FINISHED'}

# Animation frame change handler
def animation_frame_change_handler_rgp(scene):
    """Handler for frame changes during animation"""
    props = scene.rigaddon_play_props
    
    # Check if we've reached the end of playback
    if props.is_playing:
        if props.playback_mode == "ONCE" and scene.frame_current >= scene.frame_end:
            props.is_playing = False
        elif props.playback_mode == "IN_RANGE":
            if scene.frame_current >= props.playback_range_end:
                if not scene.frame_current == scene.frame_start:  # Avoid infinite loop
                    scene.frame_current = props.playback_range_start

def register():
    """Register playback control operators"""
    bpy.utils.register_class(RIGADDON_OT_play_once_rgp)
    bpy.utils.register_class(RIGADDON_OT_play_loop_rgp)
    bpy.utils.register_class(RIGADDON_OT_play_from_here_rgp)
    bpy.utils.register_class(RIGADDON_OT_play_in_range_rgp)
    bpy.utils.register_class(RIGADDON_OT_stop_playback_rgp)
    bpy.utils.register_class(RIGADDON_OT_pause_playback_rgp)
    bpy.utils.register_class(RIGADDON_OT_mute_preset_rgp)
    bpy.utils.register_class(RIGADDON_OT_solo_preset_rgp)
    
    # Add frame change handler
    bpy.app.handlers.frame_change_post.append(animation_frame_change_handler_rgp)

def unregister():
    """Unregister playback control operators"""
    # Remove frame change handler
    if animation_frame_change_handler_rgp in bpy.app.handlers.frame_change_post:
        bpy.app.handlers.frame_change_post.remove(animation_frame_change_handler_rgp)
    
    bpy.utils.unregister_class(RIGADDON_OT_solo_preset_rgp)
    bpy.utils.unregister_class(RIGADDON_OT_mute_preset_rgp)
    bpy.utils.unregister_class(RIGADDON_OT_pause_playback_rgp)
    bpy.utils.unregister_class(RIGADDON_OT_stop_playback_rgp)
    bpy.utils.unregister_class(RIGADDON_OT_play_in_range_rgp)
    bpy.utils.unregister_class(RIGADDON_OT_play_from_here_rgp)
    bpy.utils.unregister_class(RIGADDON_OT_play_loop_rgp)
    bpy.utils.unregister_class(RIGADDON_OT_play_once_rgp)
